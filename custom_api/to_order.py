import uuid
import logging
from datetime import datetime
from utils.get_market_data import fetch_bars_by_financego


def to_order(symbol, side, price=None, quantity=None, effect=None, order_type=2, in_out_market=2, channel_code=None,
             time_in_force=1, expire_time=None, hedge_flag='1', intention=None, warn_price=None, stop_price=None,
             value_date=None, maturity_date=None, close_order_id=None, pos_type=None, currency=None, bond_quote_type=9,
             stop_loss_price=None, take_profit_price=None, bp=None):
    """
    下发新的订单 - 外汇/固收交易订单API

    该函数用于向交易系统下发新的交易订单，支持外汇、固收等多种金融产品的交易。
    可以创建限价单、点击单、止损单等多种订单类型。

    必选参数:
        symbol (str): 合约唯一代码，标识要交易的金融产品
        side (str): 交易方向
            - 'B': 买入
            - 'S': 卖出

    关键参数:
        price (float, optional): 报单价格
            - 限价单必须指定价格
            - 点击单可以不指定价格
            - 默认值: None

        quantity (float, optional): 报单总数量
            - 外汇: 交易数量
            - 贵金属: 手数
            - 做市点击时需要获取当前档位量作为成交量输入
            - 默认值: None

        effect (int, optional): 开平仓类型
            - 0: 中性
            - 1: 开仓
            - 2: 平仓
            - 3: 平今 (外汇专用)
            - 4: 平昨 (外汇专用)
            - 默认值: None

        order_type (int, optional): 订单类型
            - 0: 点击单 (市价单)
            - 2: 限价单 (默认)
            - 15: SOR单 (智能订单路由)
            - 28: 直通限价单
            - 50: 择优询价单 (外汇专用)
            - 52: 止损限价单
            - 54: 止损单
            - 默认值: 2

    市场和渠道参数:
        in_out_market (int, optional): 执行市场
            - 1: 内部市场
            - 2: 外部市场 (默认)
            - 3: 内/外部市场
            - 默认值: 2

        channel_code (str, optional): 交易渠道代码
            - 指定具体的交易渠道
            - 默认值: None

    时效性参数:
        time_in_force (int, optional): 订单时效性 (SOR和点击单该字段无效)
            - 1: GTC (撤销前一直有效，默认)
            - 4: FOK (极短时间全部成交，否则全部撤销)
            - 5: FAK (极短时间成交，剩余量全部撤销)
            - 6: GFD (当日有效)
            - 7: GTD (当日有效，必须设置过期时间)
            - 默认值: 1

        expire_time (str, optional): 订单过期时间
            - GTC时格式: YYYYMMDDHHmmss
            - GFD时格式: HHmmss
            - 默认值: None

    风控和策略参数:
        hedge_flag (str, optional): 投机套保标识 (期货专用)
            - '1': 普通 (默认)
            - '2': 投机
            - '3': 套保
            - 默认值: '1'

        intention (str, optional): 交易意图
            - 可根据业务场景自定义内容
            - 默认值: None

    止损止盈参数:
        warn_price (float, optional): 止损预警价 (止损限价单使用)
            - 默认值: None

        stop_price (float, optional): 止损价
            - 默认值: None

        stop_loss_price (float, optional): 止损平仓价
            - 默认值: None

        take_profit_price (float, optional): 止盈平仓价
            - 默认值: None

    日期参数:
        value_date (str, optional): 近端起息日/近端交割日 (掉期交易使用)
            - 格式: yyyyMMdd
            - 默认值: None

        maturity_date (str, optional): 远端交割日/到期日
            - 掉期交易: 远端交割日
            - 债券交易: 到期日
            - 格式: yyyyMMdd
            - 默认值: None

    高级参数:
        close_order_id (str, optional): 平仓订单ID (逐笔模式使用)
            - 默认值: None

        pos_type (int, optional): 逐笔模式
            - 0: 不启用 (默认)
            - 1: 启用
            - 默认值: None

        currency (str, optional): 使用货币 (外汇专用)
            - 默认值: None

        bond_quote_type (int, optional): 报价方式 (债券专用)
            - 9: 连续匹配 (默认)
            - 10: 集中匹配
            - 默认值: 9

        bp (int, optional): 容忍滑点
            - 现券订单: 不输入默认10bp
            - 算法订单: 使用指定滑点
            - 非算法单: 默认为0
            - 默认值: None

    Returns:
        str: 订单唯一ID号
            - 成功: 返回订单ID字符串
            - 失败: 返回None或'0'，需查看策略日志

    使用示例:
        # 基本限价单
        order_id = to_order(
            symbol='EURUSD',
            side='B',
            price=1.1000,
            quantity=100000,
            effect=1,
            order_type=2
        )

        # 带止损止盈的订单
        order_id = to_order(
            symbol='XAUUSD',
            side='S',
            price=2000.0,
            quantity=1,
            effect=1,
            order_type=2,
            stop_loss_price=2010.0,
            take_profit_price=1990.0
        )

        # 点击单 (市价单)
        order_id = to_order(
            symbol='GBPUSD',
            side='B',
            quantity=50000,
            effect=1,
            order_type=0,
            time_in_force=5  # FAK
        )

    注意事项:
        1. 订单类型为点击单(0)时，price参数可以为None
        2. 做市点击时，quantity必须使用当前档位量，否则无法全部成交
        3. SOR单和点击单的time_in_force字段无效
        4. 使用止损限价单时需要设置warn_price
        5. 掉期交易需要设置value_date和maturity_date
        6. 逐笔模式平仓需要指定close_order_id
        7. 如果返回None或'0'，说明下单失败，需检查策略日志
    """

    # 配置日志
    logger = logging.getLogger(__name__)

    # 参数验证
    if not symbol or not isinstance(symbol, str):
        logger.error("symbol参数必须是非空字符串")
        return None

    if side not in ['B', 'S']:
        logger.error("side参数必须是'B'(买入)或'S'(卖出)")
        return None

    # 验证订单类型
    valid_order_types = [0, 2, 15, 28, 50, 52, 54]
    if order_type not in valid_order_types:
        logger.error(f"order_type参数无效，必须是{valid_order_types}中的一个")
        return None

    # 验证时效性参数
    valid_time_in_force = [1, 4, 5, 6, 7]
    if time_in_force not in valid_time_in_force:
        logger.error(f"time_in_force参数无效，必须是{valid_time_in_force}中的一个")
        return None

    # 验证开平仓类型
    if effect is not None and effect not in [0, 1, 2, 3, 4]:
        logger.error("effect参数无效，必须是0-4之间的整数")
        return None

    # 验证执行市场参数
    if in_out_market not in [1, 2, 3]:
        logger.error("in_out_market参数无效，必须是1、2或3")
        return None

    try:
        # 如果是点击单且没有提供价格，尝试获取市场价格
        # if order_type == 0:
        #     logger.info(f"点击单，获取{symbol}的市场价格")
        #
        #     # 获取最新市场数据
        #     market_data = fetch_bars_by_financego(
        #         symbol=symbol,
        #         type_=1,  # 1分钟K线获取最新价格
        #         source="MT4",
        #         count=1,
        #         startTime=-1
        #     )
        #
        #     if market_data and 'data' in market_data and market_data['data']['bars']:
        #         bars = market_data['data']['bars']
        #         latest_bar = max(bars, key=lambda x: x['time'])  # 获取最新的K线数据
        #         if 'close' in latest_bar:
        #             market_price = latest_bar['close']
        #             logger.info(f"获取到{symbol}最新价格: {market_price}")
        #
        #             # todo 根据买卖方向调整价格（滑点处理）
        #             if side == 'B':  # 买入时使用稍高价格
        #                 price = market_price * 1.0001
        #             else:  # 卖出时使用稍低价格
        #                 price = market_price * 0.9999
        #
        #             logger.info(f"调整后的下单价格: {price}")
        #         else:
        #             logger.warning("无法从市场数据中获取价格信息")
        #     else:
        #         logger.warning(f"无法获取{symbol}的市场数据")

        # 限价单必须提供价格
        if order_type == 2 and price is None:
            logger.error("限价单必须提供price参数")
            return None

        # 验证数量参数
        if quantity is not None and quantity <= 0:
            logger.error("quantity参数必须大于0")
            return None

        # 验证止损限价单的预警价格
        if order_type == 52 and warn_price is None:
            logger.error("止损限价单必须设置warn_price参数")
            return None

        # 验证掉期交易的日期参数
        if value_date is not None and maturity_date is not None:
            logger.info("检测到掉期交易参数")
            # 验证日期格式
            try:
                datetime.strptime(value_date, '%Y%m%d')
                datetime.strptime(maturity_date, '%Y%m%d')
            except ValueError:
                logger.error("value_date和maturity_date必须是yyyyMMdd格式")
                return None

        if order_type == 2:
            time_in_force = 6

        if time_in_force == 7:  # GTC
            try:
                datetime.strptime(expire_time, '%Y%m%d%H%M%S')
            except ValueError:
                logger.error("GTC模式下expire_time必须是YYYYMMDDHHmmss格式")
                return None
        else:
            expire_time = None


        # 构建订单参数字典
        order_params = {
            'symbol': symbol,
            'side': side,
            'price': price,
            'quantity': quantity,
            'effect': effect,
            'order_type': order_type,
            'in_out_market': in_out_market,
            'channel_code': channel_code,
            'time_in_force': time_in_force,
            'expire_time': expire_time,
            'hedge_flag': hedge_flag,
            'intention': intention,
            'warn_price': warn_price,
            'stop_price': stop_price,
            'value_date': value_date,
            'maturity_date': maturity_date,
            'close_order_id': close_order_id,
            'pos_type': pos_type,
            'currency': currency,
            'bond_quote_type': bond_quote_type,
            'stop_loss_price': stop_loss_price,
            'take_profit_price': take_profit_price,
            'bp': bp
        }

        # 记录订单信息
        logger.info(f"准备下单: {symbol} {side} 价格:{price} 数量:{quantity} 类型:{order_type}")

        # 模拟订单处理逻辑
        # 生成唯一订单ID
        order_id = str(uuid.uuid4()).replace('-', '')[:16]

        # 模拟订单提交
        success = _submit_order_to_exchange(order_params, order_id)

        if success:
            logger.info(f"订单提交成功，订单ID: {order_id}")
            return order_id
        else:
            logger.error("订单提交失败")
            return None

    except Exception as e:
        logger.error(f"下单过程中发生异常: {str(e)}")
        return None



def _submit_order_to_exchange(order_params: dict, order_id: str) -> bool:
    """
    模拟提交订单

    Args:
        order_params: 订单参数字典
        order_id: 订单ID

    Returns:
        bool: 提交成功返回True，否则返回False
    """
    try:
        import time
        import random

        # 模拟订单提交逻辑
        # 在实际环境中，这里会调用真实的交易所API

        print(f"模拟提交订单到交易所:")
        print(f"  订单ID: {order_id}")
        print(f"  合约: {order_params.get('symbol')}")
        print(f"  方向: {order_params.get('side')}")
        print(f"  价格: {order_params.get('price')}")
        print(f"  数量: {order_params.get('quantity')}")
        print(f"  类型: {order_params.get('order_type')}")

        # 模拟网络延迟
        time.sleep(0.1)

        # 模拟成功率（95%成功率，提高测试稳定性）
        success_rate = 0.95
        return random.random() < success_rate

    except Exception as e:
        print(f"订单提交异常: {str(e)}")
        return False
